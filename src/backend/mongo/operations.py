import logging
import datetime
import pickle
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

from django.utils import timezone

from pymongo import UpdateOne
from pymongo.results import InsertOneResult, UpdateResult
from bson import ObjectId

from mongo.conf import flows, flow_store, grid_fs
from mongo.utils import (
    status_as_dict,
    mongo_to_py_representation,
    transform_to_object_id,
    dict_status_to_enum,
    check_field_names_valid,
)
from mongo.types import FlowLogField, NodeCallField, GridFSDataType, Status, MONGO_ID

if TYPE_CHECKING:
    from flows.models import Node, Flow

logger = logging.getLogger("symmy")


# TODO: add override decorators when migrated to py3.12
class MongoLogOperations(ABC):
    @abstractmethod
    def __init__(self, *args, **kwargs):
        """
        Sets the common parameters.

        As a rule of thumb - the parameters passed to __init__ should not change after initialization.

        Warnings:
            Child classes should always call super().__init__() !
        """
        for attr in self.__slots__:
            setattr(self, attr, None)

    @abstractmethod
    def set_params(self, *args, **kwargs):
        """
        Sets parameters that will be used by other log functions.
        Should be called when any parameter that you wish to be logged changes.
        """
        pass

    @abstractmethod
    def create_log(self):
        """
        Creates a new log with the parameters set by `__init__()` or `set_params()` methods.
        """
        pass

    @abstractmethod
    def update_log(self):
        """
        Updates the existing log using the parameters set on the class.
        """
        pass


class FlowLogOperations(MongoLogOperations):
    __slots__ = tuple(FlowLogField.__members__)

    # @override
    def __init__(
        self,
        flow_instance: "Flow" = None,
        mongo_flow_id: MONGO_ID = None,
    ):
        assert flow_instance is not None or mongo_flow_id is not None, (
            "At least one of flow_instance" " or mongo_flow_id should be provided"
        )
        super().__init__()
        self.flow_instance = flow_instance
        self.mongo_flow_id = transform_to_object_id(mongo_flow_id)
        self.node_calls = []

    # @override
    def set_params(
        self,
        start: datetime.datetime = None,
        end: datetime.datetime = None,
        status: Status = None,
    ):
        if start is not None:
            self.start = start
        if end is not None:
            self.end = end
        if status is not None:
            self.status = status

    #     @override
    def create_log(self) -> InsertOneResult:
        """
        Creates a new document in the mongo collection "flows" representing a new flow run.

        Returns:
            `pymongo.results.InsertOneResult`

        Example:
            After the successful insertion the document could look like that::

                {
                    "uuid": "3249da88-00f7-4a3a-b5e7-23a46f86e92a",
                    "name": "testflow",
                    "start": "2024-05-08T13:04:55",
                    "status": {"code": 0, "name": "Success"},
                    "executed_node_count": 0,
                    "total_node_count": 2
                }

        """
        if self.start is None:
            self.start = timezone.now()

        res = flows.insert_one(self._new_log_data())
        if res.inserted_id:
            self.mongo_flow_id = res.inserted_id
        return res

    #     @override
    def update_log(self):
        """
        Updates the existing entry in the `flows` collection.
        Requires the mongo_flow_id param to be set.
        """
        assert self.mongo_flow_id, "mongo_flow_id must be set!"

        return flows.update_one(
            filter={"_id": self.mongo_flow_id}, update={"$set": self._update_log_data()}
        )

    def _new_log_data(self):
        return {
            "uuid": self.flow_instance.uuid,
            "name": self.flow_instance.name,
            "start": self.start,
            "status": status_as_dict(self.status),
            "executed_node_count": 0,
            "total_node_count": len(self.flow_instance.nodes.all()),
            "node_calls": [],
        }

    def _update_log_data(self):
        data = dict()
        if self.start is not None:
            data["start"] = self.start
        if self.end is not None:
            data["end"] = self.end
        if self.status is not None:
            data["status"] = status_as_dict(self.status)

        return data

    def _set_flow_final_status(self, node_calls: list[dict]):
        """Sets the status of the flow to successful/failed and triggers lock release"""
        # Determine final status
        if not node_calls:
            # If no node calls exist, this indicates a failure in the initial setup/connection phase
            # A successful flow should have at least one node call if it has nodes to execute
            logger.warning(f"Flow {self.mongo_flow_id} completed with no node calls - marking as failed (likely connection failure)")
            final_status = Status.failed
        elif any(
            nc.get("status").get("code") == Status.failed.value[0]
            for nc in node_calls
        ):
            # If any node call failed, mark the entire flow as failed
            logger.info(f"Flow {self.mongo_flow_id} marked as failed due to failed node calls")
            final_status = Status.failed
        else:
            # All node calls completed successfully
            logger.info(f"Flow {self.mongo_flow_id} marked as successful - all {len(node_calls)} node calls completed successfully")
            final_status = Status.success

        res = flows.update_one(
            filter={
                "_id": self.mongo_flow_id,
            },
            update={
                "$set": {
                    "end": timezone.now(),
                    "status": status_as_dict(final_status),
                }
            },
        )

        # Trigger flow completion callback to release lock
        try:
            from flows.utils import handle_flow_completion
            mongo_flow_id_str = str(self.mongo_flow_id)
            status_name = final_status.value[1].lower()  # "success" or "failed"
            handle_flow_completion(mongo_flow_id_str, status_name)
            logger.info(f"Triggered flow completion callback for {mongo_flow_id_str} with status {status_name}")
        except Exception as e:
            logger.error(f"Error triggering flow completion callback: {str(e)}")

        return res

    def _try_update_flow_final_status(self):
        """Updates the final status of a flow in case if all Nodes are either completed or failed"""
        mongo_flow_id = self.mongo_flow_id
        flow = flows.find_one(
            filter={
                "_id": mongo_flow_id,
            }
        )
        node_calls = flow.get("node_calls")
        node_calls_finished = not any(
            dict_status_to_enum(nc.get("status"))
            in (Status.in_progress, Status.in_queue)
            for nc in node_calls
        )

        if node_calls_finished:
            return self._set_flow_final_status(node_calls)
        return None

    @staticmethod
    def create_flow_log_manually(**kwargs) -> InsertOneResult:
        check_field_names_valid(kwargs, namespace=FlowLogField)
        return flows.insert_one(kwargs)

    @staticmethod
    def update_flow_log_manually(mongo_flow_id: MONGO_ID, **kwargs) -> UpdateResult:
        check_field_names_valid(kwargs, namespace=FlowLogField)
        return flows.update_one(
            filter={"_id": transform_to_object_id(mongo_flow_id)},
            update={"$set": kwargs},
        )


class NodeCallLogOperations(MongoLogOperations):
    __slots__ = tuple(NodeCallField.__members__)

    def __init__(
        self,
        flow_log_operations: FlowLogOperations,
        node_instance: "Node",
    ):
        super().__init__()
        self.flow_log_operations = flow_log_operations
        self.node_instance = node_instance

    #     @override
    def set_params(
        self,
        status: Status = None,
        errors: list[Exception] = None,
        start: datetime.datetime = None,
        end: datetime.datetime = None,
        request_count: int = None,
        retries: int = None,
        raw_data: MONGO_ID = None,
        output_data_ids: list[MONGO_ID] = None,
        input_data_ids: list[MONGO_ID] = None,
    ):
        if status is not None:
            self.status = status
        if errors is not None:
            self.errors = errors
        if start is not None:
            self.start = start
        if end is not None:
            self.end = end
        if request_count is not None:
            self.request_count = request_count
        if retries is not None:
            self.retries = retries
        if raw_data is not None:
            self.raw_data = transform_to_object_id(raw_data)
        if output_data_ids is not None:
            self.output_data_ids = [
                transform_to_object_id(id_) for id_ in output_data_ids
            ]
        if input_data_ids is not None:
            self.input_data_ids = [
                transform_to_object_id(id_) for id_ in input_data_ids
            ]

    #     @override
    def create_log(self) -> UpdateResult:
        """
        Adds a new node call to the existing document in the `flows` collection.

        Returns:
            `UpdateResult`, as the document in the `flows` collection is being updated,
             node_calls is just a list entry in it.

        Example:
            After the successful insertion the node call could look like that::

                {
                    "uuid": "0f60c721-6a59-453a-999a-e7e19aee834b",
                    "start: "2024-05-09T11:00:38",
                    "input_data": ["1337abobaaboba123aslcl"]
                    "raw_data": None,
                    "output_data": None,
                    "status": {"code": 3, "name": "In progress"},
                    "request_count": 0,
                    "retries": 0,
                    "errors": [],
                }
        """
        return self._process_node_call_log(new_node_call=True)

    # @override
    def update_log(self) -> UpdateResult:
        """
        Updates a specific node call.

        Uses `flow_log_operations.mongo_flow_id`
        and `node_instance.uuid` parameters to find the record to update.

        Returns:
            `UpdateResult`, as the document in the `flows` collection is being updated,
             node_calls is just a list entry in it.
        """
        return self._process_node_call_log()

    def is_node_completed(self):
        return self.status in (Status.failed, Status.success)

    def _process_node_call_log(self, new_node_call: bool = False) -> UpdateResult:
        update_operation: UpdateOne = self._create_node_call_log_sql_operation(
            new_node_call=new_node_call
        )

        if not self.is_node_completed():
            return self._update_node_calls_list(
                filter_=update_operation._filter,  # noqa
                data=update_operation._doc,  # noqa
            )

        return self._update_node_call_log_finished(
            node_call_data=update_operation._doc,  # noqa,
        )

    def _update_node_call_log_finished(self, node_call_data: dict) -> UpdateResult:
        """Handles the update of the finished node call."""
        sql_operations: list[UpdateOne] = []

        if self.end is None:
            node_call_data["$set"]["node_calls.$.end"] = timezone.now()
        node_call_data.update({"$inc": {"executed_node_count": 1}})

        res = self._update_node_calls_list(
            filter_=self._update_filter(),
            data=node_call_data,
        )

        if self.status == Status.success and len(self.node_instance.children) != 0:
            sql_operations.extend(self._create_child_node_calls_log_ops())
            flows.bulk_write(requests=sql_operations)

        self.flow_log_operations._try_update_flow_final_status()
        return res

    def _new_node_call_data(self) -> dict:
        """Returns the data for a new node call."""
        return {
            "uuid": self.node_instance.uuid,
            "start": self.start,
            "status": status_as_dict(self.status),
            "retries": self.retries,
            "errors": (
                self.errors
                if self.errors is None
                else [str(err) for err in self.errors]
            ),
            "raw_data": self.raw_data,
            "input_data": self.input_data_ids,
            "output_data": self.output_data_ids,
            "request_count": self.request_count,
            "end": self.end,
        }

    def _update_node_call_data(self) -> dict:
        """Returns the data for a node call that should be updated."""
        node_call_log = {
            "status": status_as_dict(self.status),
        }
        if self.start is not None:
            node_call_log["start"] = self.start
        if self.end is not None:
            node_call_log["end"] = self.end
        if self.errors is not None:
            node_call_log["errors"] = [str(err) for err in self.errors]
        if self.retries is not None:
            node_call_log["retries"] = self.retries
        if self.raw_data is not None:
            node_call_log["raw_data"] = self.raw_data
        if self.input_data_ids is not None:
            node_call_log["input_data"] = self.input_data_ids
        if self.output_data_ids is not None:
            node_call_log["output_data"] = self.output_data_ids
        if self.request_count is not None:
            node_call_log["request_count"] = self.request_count

        return node_call_log

    def _create_node_call_log_sql_operation(
        self, new_node_call: bool = True
    ) -> UpdateOne:
        """
        Creates an `UpdateOne` sql operation which when executed
        adds a new node call to the existing document in the `flows` collection.

        Args:
            new_node_call(bool): Whether to create a new node or update an existing one.

        Returns:
            `UpdateOne` sql operation.
        """
        if new_node_call:
            node_call_log = self._new_node_call_data()
            new_node_call_data = {"$push": {"node_calls": node_call_log}}
            return UpdateOne(
                filter=self._insertion_fiter(),
                update=new_node_call_data,
            )

        node_call_log = self._update_node_call_data()
        update_node_call_data = {"$set": {}}
        for k, v in node_call_log.items():
            update_node_call_data["$set"]["node_calls.$." + k] = v

        return UpdateOne(
            filter=self._update_filter(),
            update=update_node_call_data,
        )

    def _update_filter(self):
        return {**self._insertion_fiter(), "node_calls.uuid": self.node_instance.uuid}

    def _insertion_fiter(self):
        return {"_id": self.flow_log_operations.mongo_flow_id}

    @staticmethod
    def _update_node_calls_list(filter_: dict, data: dict) -> UpdateResult:
        """Updates the `node_calls` attribute of a specific flow document."""
        return flows.update_one(
            filter=filter_,
            update=data,
        )

    def _create_child_node_calls_log_ops(self) -> list[UpdateOne]:
        """Creates `UpdateOne` operations for all children of a particular `Node` instance."""
        ops: list[UpdateOne] = []
        temp_nclog_operations = NodeCallLogOperations(
            flow_log_operations=self.flow_log_operations, node_instance=None  # noqa
        )
        temp_nclog_operations.set_params(status=Status.in_queue)

        for child in self.node_instance.children:
            temp_nclog_operations.node_instance = child
            ops.append(temp_nclog_operations._create_node_call_log_sql_operation())

        return ops

    @staticmethod
    def create_node_call_manually(mongo_flow_id: MONGO_ID, **kwargs) -> UpdateResult:
        check_field_names_valid(kwargs, namespace=NodeCallField)
        return flows.update_one(
            filter={"_id": transform_to_object_id(mongo_flow_id)},
            update={"$push": {"node_calls": {**kwargs}}},
        )

    @staticmethod
    def update_node_call_manually(
        mongo_flow_id: MONGO_ID, node_uuid, **kwargs
    ) -> UpdateResult:
        check_field_names_valid(kwargs, namespace=NodeCallField)
        update_node_call_data = {"$set": {}}
        for k, v in kwargs.items():
            update_node_call_data["$set"]["node_calls.$." + k] = v

        return flows.update_one(
            filter={
                "_id": transform_to_object_id(mongo_flow_id),
                "node_calls.uuid": node_uuid,
            },
            update=update_node_call_data,
        )


class GridFSOperations:
    @staticmethod
    def store_data(
        data, data_type: GridFSDataType, obj_id_as_str: bool = False
    ) -> ObjectId | str:
        """
        Stores data in the GridFS storage

        Args:
            data (Any): Arbitrary data to store.
            data_type (GridFSDataType): Type of data to be stored.
                                        If GridFSDataType.serializable_data is chosen,
                                        then the data will be deserialized, if get_node_data is used.
            obj_id_as_str (bool): Get object id as string.

        Returns:
            `ObjectId`, if obj_id_as_str is set to False, str(ObjectId) otherwise.
        """
        metadata = {"data_type": data_type.name, "python_type": data.__class__.__name__}
        data = pickle.dumps(data)
        res = grid_fs.put(data, metadata=metadata)
        return res if not obj_id_as_str else str(res)

    @staticmethod
    def delete_data(file_id: MONGO_ID) -> None:
        """
        Deletes data from GridFS storage

        Args:
            file_id (str | ObjectId): ObjectID of a GridFS document to delete
        """
        # TODO: somehow prohibit reading while deleting
        # this can occur e.g. during during admin flow page refresh and retrying node writing new raw data
        if file_id:
            grid_fs.delete(transform_to_object_id(file_id))

    @staticmethod
    def get_data(file_ids: MONGO_ID | list[MONGO_ID]) -> list[dict]:
        """
        Gets data from GridFS.

        Args:
            file_ids (str | list[str] | ObjectId | list[ObjectId]): ObjectIDs of GridFS chunks to get.

        Returns:
            list[dict]: dictionaries with data(python objects) which were stored in the GridFS

            Example::

                {
                    "data": file data
                    "data_type": `GridFSDataType` member name (e.g. 'serializable_data')
                    "python_type": original python type
                }

            file data is either the original object converted to binary
            or the original object, if it can be unpickled using
            pickle.loads() with "utf-8" encoding
        """
        file_ids = file_ids if isinstance(file_ids, list) else [file_ids]
        file_ids = [transform_to_object_id(id_) for id_ in file_ids]
        res = list()
        for fid in file_ids:
            file_object = grid_fs.get(fid)
            file_data = file_object.read()
            metadata = file_object.metadata
            if metadata["data_type"] == GridFSDataType.serializable_data.name:
                try:
                    file_data = pickle.loads(file_data, encoding="utf-8")
                except pickle.PickleError:
                    pass
            res.append(
                {
                    "data": file_data,
                    "data_type": metadata["data_type"],
                    "python_type": metadata["python_type"],
                }
            )

        return res


def store_flow_data(flow_instance: "Flow", data: dict) -> UpdateResult:
    """
    Stores data in the flow_store collection.
    Args:
        flow_instance (flows.models.Flow): flow instance
        data (dict): dict with key-value pairs to be stored

    Returns: UpdateResult
    """

    query = {"uuid": flow_instance.uuid}
    data.update({"uuid": flow_instance.uuid})
    result = flow_store.replace_one(query, data, upsert=True)
    logger.info(f"Updating data store: {result}")
    return result


def get_flow_store_data(flow_instance: "Flow", json_serializable: bool = False) -> dict:
    res = flow_store.find_one({"uuid": flow_instance.uuid}) or {}
    if json_serializable:
        return mongo_to_py_representation(res)
    return res
