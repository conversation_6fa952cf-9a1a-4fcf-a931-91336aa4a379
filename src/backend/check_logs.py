#!/usr/bin/env python
"""
Check logs for the specific flow execution to understand what happened.
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'symmy.settings')
django.setup()

from bson import ObjectId
from mongo.conf import flows
from mongo.utils import mongo_to_py_representation
from mongo.types import Status
import subprocess
import re

def check_flow_logs(flow_id_str):
    """Check logs for a specific flow execution."""
    print(f"Checking logs for flow: {flow_id_str}")
    print("=" * 50)
    
    # Get the flow document to find the time range
    try:
        flow_id = ObjectId(flow_id_str)
        flow_doc = flows.find_one({"_id": flow_id})
        
        if not flow_doc:
            print(f"❌ Flow {flow_id_str} not found")
            return
            
        start_time = flow_doc.get('start')
        end_time = flow_doc.get('end')
        
        print(f"Flow execution time: {start_time} to {end_time}")
        
        # Search for logs related to this flow
        search_patterns = [
            flow_id_str,
            str(flow_doc.get('uuid', '')),
            flow_doc.get('name', ''),
        ]
        
        print("\n🔍 Searching for relevant log entries...")
        
        # Try to find logs in Docker container
        try:
            # Search for logs containing the flow ID
            cmd = f"docker-compose logs --since='2025-05-30T08:40:00' --until='2025-05-30T08:45:00' web celery_worker"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logs = result.stdout
                
                # Filter logs for our flow
                relevant_logs = []
                for line in logs.split('\n'):
                    for pattern in search_patterns:
                        if pattern and pattern in line:
                            relevant_logs.append(line)
                            break
                
                if relevant_logs:
                    print(f"📋 Found {len(relevant_logs)} relevant log entries:")
                    for log in relevant_logs[:20]:  # Show first 20 entries
                        print(f"  {log}")
                else:
                    print("❌ No relevant logs found in Docker logs")
                    
                # Also search for our specific log messages
                flow_status_logs = []
                for line in logs.split('\n'):
                    if any(phrase in line for phrase in [
                        "_set_flow_final_status",
                        "completed with no node calls",
                        "marked as failed",
                        "marked as successful",
                        "Triggered flow completion"
                    ]):
                        flow_status_logs.append(line)
                
                if flow_status_logs:
                    print(f"\n📊 Flow status related logs:")
                    for log in flow_status_logs:
                        print(f"  {log}")
                        
            else:
                print(f"❌ Error getting Docker logs: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Timeout getting Docker logs")
        except Exception as e:
            print(f"❌ Error searching logs: {str(e)}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    flow_id = "68396f8965eda73896dd6390"
    check_flow_logs(flow_id)
