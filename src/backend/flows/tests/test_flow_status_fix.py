"""
Test for the flow status bug fix where flows with connection failures
were incorrectly marked as successful instead of failed.
"""
import unittest
from unittest.mock import patch, MagicMock
from bson import ObjectId

from django.test import TestCase
from django.utils import timezone

from mongo.operations import FlowLogOperations
from mongo.types import Status
from flows.models import Flow
from core.models import Project, Organization


class TestFlowStatusFix(TestCase):
    """Test that flows with no node calls are correctly marked as failed."""

    def setUp(self):
        """Set up test data."""
        self.organization = Organization.objects.create(name="Test Org")
        self.project = Project.objects.create(
            name="Test Project",
            organization=self.organization
        )
        self.flow = Flow.objects.create(
            name="Test Flow",
            project=self.project
        )

    def test_flow_with_no_node_calls_marked_as_failed(self):
        """Test that a flow with no node calls is marked as failed."""
        # Create a flow log operations instance
        flow_log_ops = FlowLogOperations(flow_instance=self.flow)
        
        # Mock the MongoDB flow document with no node calls
        mock_flow_doc = {
            "_id": ObjectId(),
            "uuid": str(self.flow.uuid),
            "name": self.flow.name,
            "start": timezone.now(),
            "status": {"code": Status.in_progress.value[0], "name": Status.in_progress.value[1]},
            "executed_node_count": 0,
            "total_node_count": 1,
            "node_calls": []  # Empty node calls - this is the bug scenario
        }
        
        # Mock the flows collection
        with patch('mongo.operations.flows') as mock_flows:
            mock_flows.find_one.return_value = mock_flow_doc
            mock_flows.update_one.return_value = MagicMock()
            
            # Set the mongo_flow_id
            flow_log_ops.mongo_flow_id = mock_flow_doc["_id"]
            
            # Call _set_flow_final_status with empty node_calls
            result = flow_log_ops._set_flow_final_status([])
            
            # Verify that flows.update_one was called with failed status
            mock_flows.update_one.assert_called_once()
            call_args = mock_flows.update_one.call_args
            
            # Check the update parameters
            update_data = call_args[1]['update']['$set']
            self.assertEqual(update_data['status']['code'], Status.failed.value[0])
            self.assertEqual(update_data['status']['name'], Status.failed.value[1])

    def test_flow_with_failed_node_calls_marked_as_failed(self):
        """Test that a flow with failed node calls is marked as failed."""
        flow_log_ops = FlowLogOperations(flow_instance=self.flow)
        
        # Mock node calls with one failed node
        node_calls = [
            {
                "uuid": "test-node-1",
                "status": {"code": Status.failed.value[0], "name": Status.failed.value[1]},
                "start": timezone.now(),
                "end": timezone.now(),
                "errors": ["Connection failed"]
            }
        ]
        
        with patch('mongo.operations.flows') as mock_flows:
            mock_flows.update_one.return_value = MagicMock()
            flow_log_ops.mongo_flow_id = ObjectId()
            
            # Call _set_flow_final_status with failed node calls
            result = flow_log_ops._set_flow_final_status(node_calls)
            
            # Verify that flows.update_one was called with failed status
            mock_flows.update_one.assert_called_once()
            call_args = mock_flows.update_one.call_args
            
            update_data = call_args[1]['update']['$set']
            self.assertEqual(update_data['status']['code'], Status.failed.value[0])
            self.assertEqual(update_data['status']['name'], Status.failed.value[1])

    def test_flow_with_successful_node_calls_marked_as_success(self):
        """Test that a flow with all successful node calls is marked as success."""
        flow_log_ops = FlowLogOperations(flow_instance=self.flow)
        
        # Mock node calls with successful nodes
        node_calls = [
            {
                "uuid": "test-node-1",
                "status": {"code": Status.success.value[0], "name": Status.success.value[1]},
                "start": timezone.now(),
                "end": timezone.now(),
                "errors": []
            },
            {
                "uuid": "test-node-2", 
                "status": {"code": Status.success.value[0], "name": Status.success.value[1]},
                "start": timezone.now(),
                "end": timezone.now(),
                "errors": []
            }
        ]
        
        with patch('mongo.operations.flows') as mock_flows:
            mock_flows.update_one.return_value = MagicMock()
            flow_log_ops.mongo_flow_id = ObjectId()
            
            # Call _set_flow_final_status with successful node calls
            result = flow_log_ops._set_flow_final_status(node_calls)
            
            # Verify that flows.update_one was called with success status
            mock_flows.update_one.assert_called_once()
            call_args = mock_flows.update_one.call_args
            
            update_data = call_args[1]['update']['$set']
            self.assertEqual(update_data['status']['code'], Status.success.value[0])
            self.assertEqual(update_data['status']['name'], Status.success.value[1])

    def test_try_update_flow_final_status_with_empty_node_calls(self):
        """Test _try_update_flow_final_status when node_calls is empty."""
        flow_log_ops = FlowLogOperations(flow_instance=self.flow)
        
        # Mock the MongoDB flow document with empty node_calls
        mock_flow_doc = {
            "_id": ObjectId(),
            "node_calls": []  # Empty - should trigger failed status
        }
        
        with patch('mongo.operations.flows') as mock_flows:
            mock_flows.find_one.return_value = mock_flow_doc
            mock_flows.update_one.return_value = MagicMock()
            
            flow_log_ops.mongo_flow_id = mock_flow_doc["_id"]
            
            # Call _try_update_flow_final_status
            result = flow_log_ops._try_update_flow_final_status()
            
            # Should call update_one with failed status
            mock_flows.update_one.assert_called_once()
            call_args = mock_flows.update_one.call_args
            
            update_data = call_args[1]['update']['$set']
            self.assertEqual(update_data['status']['code'], Status.failed.value[0])
            self.assertEqual(update_data['status']['name'], Status.failed.value[1])

    def tearDown(self):
        """Clean up test data."""
        # Clean up in reverse order of creation
        self.flow.delete()
        self.project.delete()
        self.organization.delete()


if __name__ == '__main__':
    unittest.main()
