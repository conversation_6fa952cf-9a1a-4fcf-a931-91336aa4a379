#!/usr/bin/env python
"""
Test script to verify the flow status fix is working.
"""
import os
import sys
import django
import time

# Add the project root to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'symmy.settings')
django.setup()

from flows.models import Flow
from bson import ObjectId
from mongo.conf import flows

def test_flow_execution():
    """Test flow execution to see if connection failures are properly handled."""
    print("Testing flow execution with connection failure...")
    print("=" * 60)
    
    try:
        # Find the "Timeout 3min" flow
        flow = Flow.objects.filter(name="Timeout 3min").first()
        
        if not flow:
            print("❌ Could not find 'Timeout 3min' flow")
            return
            
        print(f"✅ Found flow: {flow.name} (UUID: {flow.uuid})")
        
        # Execute the flow
        print("🚀 Executing flow...")
        mongo_flow_id = flow.execute()
        
        print(f"📝 Flow execution started with mongo_flow_id: {mongo_flow_id}")
        
        # Wait a bit for execution to complete
        print("⏳ Waiting for flow execution to complete...")
        time.sleep(5)
        
        # Check the final status
        flow_doc = flows.find_one({"_id": ObjectId(mongo_flow_id)})
        
        if flow_doc:
            status = flow_doc.get('status', {})
            node_calls = flow_doc.get('node_calls', [])
            
            print(f"\n📊 Flow Execution Results:")
            print(f"   Status: {status.get('name', 'Unknown')} (code: {status.get('code', 'Unknown')})")
            print(f"   Node calls count: {len(node_calls)}")
            print(f"   Start: {flow_doc.get('start', 'Unknown')}")
            print(f"   End: {flow_doc.get('end', 'Unknown')}")
            
            # Analyze the result
            if not node_calls and status.get('code') == 1:  # Failed
                print("✅ SUCCESS: Flow with no node calls correctly marked as FAILED")
                print("   This indicates the fix is working!")
            elif not node_calls and status.get('code') == 0:  # Success
                print("❌ BUG STILL EXISTS: Flow with no node calls incorrectly marked as SUCCESS")
                print("   The fix is not working properly.")
            elif node_calls:
                print(f"ℹ️  Flow has {len(node_calls)} node calls - analyzing...")
                failed_nodes = [nc for nc in node_calls if nc.get('status', {}).get('code') == 1]
                if failed_nodes:
                    print(f"   {len(failed_nodes)} node(s) failed")
                    if status.get('code') == 1:
                        print("✅ Flow with failed nodes correctly marked as FAILED")
                    else:
                        print("❌ Flow with failed nodes incorrectly marked as SUCCESS")
                else:
                    print("   All nodes succeeded")
                    if status.get('code') == 0:
                        print("✅ Flow with successful nodes correctly marked as SUCCESS")
                    else:
                        print("❌ Flow with successful nodes incorrectly marked as FAILED")
            
            return mongo_flow_id
            
        else:
            print(f"❌ Could not find flow document with ID: {mongo_flow_id}")
            return None
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_flow_execution()
