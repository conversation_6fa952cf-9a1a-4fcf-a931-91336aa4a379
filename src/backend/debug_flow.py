#!/usr/bin/env python
"""
Debug script to investigate the specific flow run that's still showing the issue.
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'symmy.settings')
django.setup()

from bson import ObjectId
from mongo.conf import flows
from mongo.utils import mongo_to_py_representation
from mongo.types import Status

def investigate_flow(flow_id_str):
    """Investigate a specific flow execution."""
    print(f"Investigating flow: {flow_id_str}")
    print("=" * 50)
    
    try:
        # Convert string to ObjectId
        flow_id = ObjectId(flow_id_str)
        
        # Query the flow document
        flow_doc = flows.find_one({"_id": flow_id})
        
        if not flow_doc:
            print(f"❌ Flow {flow_id_str} not found in MongoDB")
            return
            
        print(f"✅ Flow found: {flow_doc.get('name', 'Unknown')}")
        print(f"📊 Status: {flow_doc.get('status', {})}")
        print(f"🔢 Total nodes: {flow_doc.get('total_node_count', 0)}")
        print(f"✅ Executed nodes: {flow_doc.get('executed_node_count', 0)}")
        print(f"📅 Start: {flow_doc.get('start', 'Unknown')}")
        print(f"📅 End: {flow_doc.get('end', 'Unknown')}")
        
        # Check node_calls
        node_calls = flow_doc.get('node_calls', [])
        print(f"🔗 Node calls count: {len(node_calls)}")
        
        if not node_calls:
            print("⚠️  NO NODE CALLS FOUND - This should be marked as FAILED!")
            print("   This indicates a connection failure or early termination.")
        else:
            print("📋 Node calls details:")
            for i, node_call in enumerate(node_calls):
                status = node_call.get('status', {})
                print(f"   Node {i+1}: {status.get('name', 'Unknown')} (code: {status.get('code', 'Unknown')})")
                if node_call.get('errors'):
                    print(f"      Errors: {node_call.get('errors')}")
        
        # Check if this matches our expected fix behavior
        status_code = flow_doc.get('status', {}).get('code')
        status_name = flow_doc.get('status', {}).get('name')
        
        print("\n🔍 Analysis:")
        if not node_calls and status_code == Status.success.value[0]:
            print("❌ BUG CONFIRMED: Flow has no node calls but is marked as SUCCESS")
            print("   Expected: Should be marked as FAILED")
        elif not node_calls and status_code == Status.failed.value[0]:
            print("✅ CORRECT: Flow has no node calls and is correctly marked as FAILED")
        elif node_calls:
            failed_nodes = [nc for nc in node_calls if nc.get('status', {}).get('code') == Status.failed.value[0]]
            if failed_nodes and status_code == Status.success.value[0]:
                print("❌ BUG: Flow has failed nodes but is marked as SUCCESS")
            elif failed_nodes and status_code == Status.failed.value[0]:
                print("✅ CORRECT: Flow has failed nodes and is correctly marked as FAILED")
            elif not failed_nodes and status_code == Status.success.value[0]:
                print("✅ CORRECT: Flow has successful nodes and is correctly marked as SUCCESS")
        
        print(f"\n📄 Raw document (formatted):")
        formatted_doc = mongo_to_py_representation(flow_doc)
        import json
        print(json.dumps(formatted_doc, indent=2, default=str))
        
    except Exception as e:
        print(f"❌ Error investigating flow: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # The flow ID from the user's report
    flow_id = "68396f8965eda73896dd6390"
    investigate_flow(flow_id)
